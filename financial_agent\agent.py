from google.adk.agents import Agent, LlmAgent
from .tools import postgres_query_tool, get_sao_paulo_datetime
from .tools import rag_query
from google.genai import types
from .prompt import ESPECIALISTA_FINANCEIRO_PROMPT
from google.adk.tools import agent_tool

especialista_financeiro = Agent(
   model="gemini-2.5-flash-preview-04-17",
   name="especialista_financeiro", 
   instruction=ESPECIALISTA_FINANCEIRO_PROMPT,
   description="Especialista do mercado financeiro que consegue trazer dados atualizados.",
   generate_content_config=types.GenerateContentConfig(
       
   max_output_tokens=5000
   ),
   tools=[rag_query, postgres_query_tool, get_sao_paulo_datetime],
)

root_agent = especialista_financeiro