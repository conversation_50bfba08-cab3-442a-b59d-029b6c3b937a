[project]
name = "financial_agent"
version = "0.1.0"
description = "An Agent that helps financial professionals"
authors = [
    {name = "Ciro", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<3.13"
license = "Apache License 2.0"
dependencies = [
    "requests>=2.31.0",
    "google-adk>=0.1.0",
    "pydantic>=2.11.3",
    "python-dotenv>=1.1.0",
    "google-cloud-aiplatform[adk,agent_engines]>=1.42.1",
    "absl-py>=2.1.0",
    "cloudpickle>=3.0.0",   
    "psycopg2-binary>=2.9.9",
    "pytz>=2023.3",
]

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
requests = "^2.31.0"
google-adk = "^0.1.0"
pydantic = "^2.11.3"
python-dotenv = "^1.1.0"
google-cloud-aiplatform = {extras = ["adk", "agent_engines"], version = "^1.42.1"}
absl-py = "^2.1.0"
cloudpickle = "^3.0.0"
psycopg2-binary = "^2.9.9"
pytz = "^2023.3"

[tool.poetry]
packages = [{include = "financial_agent"}]

[tool.poetry.scripts]
deploy-local = "deployment.local:main"
deploy-remote = "deployment.remote:main"
cleanup = "deployment.cleanup:cleanup_deployment"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

